/* eslint-disable @typescript-eslint/no-explicit-any */
import NextAuth from 'next-auth/next'
import CredentialsProvider from 'next-auth/providers/credentials'
import GoogleProvider from 'next-auth/providers/google'
import FacebookProvider from 'next-auth/providers/facebook'

const handler = NextAuth({
  providers: [
    // Email/Password Provider using your IAM backend
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        console.log(`URL: `, `${process.env.API_URL || 'http://localhost:3001/api'}/auth/login`)

        try {
          // Call your IAM backend login endpoint
          const response = await fetch(`${process.env.API_URL || 'http://localhost:3001/api'}/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password
            })
          })

          console.log(response)

          const data = await response.json()

          if (response.ok && data.success) {
            // Return user object that will be stored in the JWT
            return {
              id: data.data.user.id,
              email: data.data.user.email,
              username: data.data.user.username,
              firstName: data.data.user.firstName,
              lastName: data.data.user.lastName,
              backendToken: data.data.token // Store backend JWT token
            }
          }

          return null
        } catch (error) {
          console.error('Authentication error:', error)
          return null
        }
      }
    }),

    // Google Provider
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!
    }),

    // Facebook Provider
    FacebookProvider({
      clientId: process.env.FACEBOOK_CLIENT_ID!,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET!
    })
  ],

  callbacks: {
    async signIn({ user, account }) {
      // Handle OAuth providers (Google, Facebook)
      if (account?.provider === 'google' || account?.provider === 'facebook') {
        try {
          // Check if user exists in your IAM backend
          const checkResponse = await fetch(`${process.env.API_URL || 'http://localhost:3001/api'}/users/by-email`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email: user.email })
          })

          if (checkResponse.ok) {
            // User exists, get their backend token
            const loginResponse = await fetch(
              `${process.env.API_URL || 'http://localhost:3001/api'}/auth/oauth-login`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  email: user.email,
                  provider: account.provider,
                  providerId: account.providerAccountId
                })
              }
            )

            if (loginResponse.ok) {
              const loginData = await loginResponse.json()
              ;(user as any).backendToken = loginData.data.token
              user.id = loginData.data.user.id
            } else {
              console.error('OAuth Login failed:', loginResponse.text())
              return false
            }
          } else {
            if (user.email === undefined || !user.email) {
              console.error('User email is undefined')
              return false
            }

            // User doesn't exist, create them in your IAM backend
            const createResponse = await fetch(
              `${process.env.API_URL || 'http://localhost:3001/api'}/auth/oauth-register`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  email: user.email,
                  username: user.email.split('@')[0] + '_' + account.provider,
                  firstName: user.name?.split(' ')[0] || '',
                  lastName: user.name?.split(' ').slice(1).join(' ') || '',
                  provider: account.provider,
                  providerId: account.providerAccountId,
                  image: user.image
                })
              }
            )

            if (createResponse.ok) {
              const createData = await createResponse.json()
              if (createData.success) {
                ;(user as any).backendToken = createData.data.token
                user.id = createData.data.user.id
              } else {
                console.error('OAuth Register failed:', createData.error)
                return false
              }
            }
          }

          return true
        } catch (error) {
          console.error('OAuth sign-in error:', error)
          return false
        }
      }

      return true
    },

    async jwt({ token, user }) {
      // Store backend token in JWT
      if ((user as any)?.backendToken) {
        ;(token as any).backendToken = (user as any).backendToken
        ;(token as any).userId = user.id
      }

      return token
    },

    async session({ session, token }) {
      // Add backend token to session
      if ((token as any).backendToken) {
        ;(session as any).backendToken = (token as any).backendToken
        if (session.user) {
          ;(session.user as any).id = (token as any).userId
        }
      }

      return session
    }
  },

  pages: {
    signIn: '/signin'
  },

  session: {
    strategy: 'jwt'
  },

  secret: process.env.NEXTAUTH_SECRET
})

export { handler as GET, handler as POST }
